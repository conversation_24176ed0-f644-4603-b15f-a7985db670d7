name: metadata model generate
on:
  push:
    branches:
      - master
    paths:
      - "metadata-models/**"
  release:
    types: [published]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      publish: ${{ steps.publish.outputs.publish }}
    steps:
      - name: Check whether upload to datahub is enabled
        id: publish
        run: |
          echo "Enable publish: ${{ github.repository == 'datahub-project/datahub' }}"
          echo "publish=${{ github.repository == 'datahub-project/datahub' }}" >> $GITHUB_OUTPUT
  metadata-ingestion-docgen:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: 17
      - uses: gradle/actions/setup-gradle@v4
      - uses: acryldata/sane-checkout-action@v3
      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: "pip"
      - name: Install dependencies
        run: ./metadata-ingestion/scripts/install_deps.sh
      - name: Run model generation
        run: ./gradlew :metadata-models:build
      - name: Generate metadata files
        if: ${{ needs.setup.outputs.publish == 'true' }}
        run: ./gradlew :metadata-ingestion:modelDocGen
      - name: Configure AWS Credentials
        if: ${{ needs.setup.outputs.publish == 'true' }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.ACRYL_CI_ARTIFACTS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.ACRYL_CI_ARTIFACTS_ACCESS_KEY }}
          aws-region: us-west-2
      - name: Upload metadata to S3
        if: ${{ needs.setup.outputs.publish == 'true' }}
        run: aws s3 cp ./metadata-ingestion/generated/docs/metadata_model_mces.json s3://${{ secrets.ACRYL_CI_ARTIFACTS_BUCKET }}/datahub/demo/metadata/
      - name: Upload metadata to DataHub
        if: ${{ needs.setup.outputs.publish == 'true' }}
        env:
          DATAHUB_SERVER: ${{ secrets.DataHubServer }}
          DATAHUB_TOKEN: ${{ secrets.DataHubToken }}
          DATAHUB_TELEMETRY_ENABLED: false
        run: ./gradlew :metadata-ingestion:modelDocUpload
