plugins {
  id 'scala'
  id 'org.gradle.playframework'
}

apply from: '../gradle/versioning/versioning.gradle'
apply from: './play.gradle'
apply from: '../gradle/coverage/java-coverage.gradle'
apply from: '../gradle/docker/docker.gradle'

ext {
  docker_repo = 'datahub-frontend-react'
  docker_dir = 'datahub-frontend'
}

java {
  toolchain {
    languageVersion = JavaLanguageVersion.of(jdkVersion(project))
  }
}

test {
  jacoco {
    // jacoco instrumentation is failing when dealing with code of this dependency, excluding it.
    excludes = ["com/gargoylesoftware/**"]
  }
}

model {
  // Must specify the dependency here as "stage" is added by rule based model.
  tasks.myTar {
    dependsOn stage
  }
}

task myTar(type: Tar) {
  compression = Compression.NONE

  from("${buildDir}/stage")

  into("bin") {
    from("bin")
  }

  into("conf") {
    from("conf")
    fileMode = 0600
  }
}

artifacts {
  archives myTar
}

/*
PLAY UPGRADE NOTE
Generates the distribution jars under the expected names. The playFramework plugin only accepts certain name values
for the resulting folders and files, so some changes were made to accommodate. Default distribution is main if these are excluded
 */
distributions {
  create("datahub-frontend") {
    distributionBaseName = project.ext.playBinaryBaseName
  }
  playBinary {
    distributionBaseName = project.ext.playBinaryBaseName
  }
}

task unversionZip(type: Copy, dependsOn: [':datahub-web-react:distZip', distZip]) {

  from ("${buildDir}/distributions")
  include "datahub-frontend-${version}.zip"
  into "${buildDir}/distributions"
  rename "datahub-frontend-${version}.zip", "datahub-frontend.zip"
}

docker {
  dependsOn(stageMainDist)
  name "${docker_registry}/${docker_repo}:${versionTag}"
  dockerfile file("${rootProject.projectDir}/docker/${docker_dir}/Dockerfile")
  files "${buildDir}/stage"
  files fileTree(rootProject.projectDir) {
    include '.dockerignore'
    include 'docker/monitoring/*'
    include "docker/${docker_dir}/*"
  }.exclude {
    i -> (!i.file.name.endsWith(".dockerignore") && i.file.isHidden())
  }
  additionalTag("Debug", "${docker_registry}/${docker_repo}:debug")

  // Add build args if they are defined (needed for some CI or enterprise environments)
  def dockerBuildArgs = [:]
  if (project.hasProperty('alpineApkRepositoryUrl')) {
    dockerBuildArgs.ALPINE_REPO_URL = project.getProperty('alpineApkRepositoryUrl')
  }
  if (project.hasProperty('githubMirrorUrl')) {
    dockerBuildArgs.GITHUB_REPO_URL = project.getProperty('githubMirrorUrl')
  }
  if (project.hasProperty('mavenCentralRepositoryUrl')) {
    dockerBuildArgs.MAVEN_CENTRAL_REPO_URL = project.getProperty('mavenCentralRepositoryUrl')
  }

  if (dockerBuildArgs.size() > 0) {
    buildArgs(dockerBuildArgs)
  }
}


// gradle 8 fixes
tasks.getByName('createDatahub-frontendTarDist').dependsOn 'stageMainDist'
tasks.getByName('createDatahub-frontendZipDist').dependsOn 'stageMainDist'
stagePlayBinaryDist.dependsOn tasks.getByName('createDatahub-frontendStartScripts')
playBinaryDistTar.dependsOn tasks.getByName('createDatahub-frontendStartScripts')
playBinaryDistZip.dependsOn tasks.getByName('createDatahub-frontendStartScripts')
tasks.getByName('stageDatahub-frontendDist').dependsOn stagePlayBinaryDist
tasks.getByName('stageDatahub-frontendDist').dependsOn createPlayBinaryStartScripts
tasks.getByName('datahub-frontendDistTar').dependsOn createPlayBinaryStartScripts
tasks.getByName('datahub-frontendDistTar').dependsOn createMainStartScripts
tasks.getByName('datahub-frontendDistZip').dependsOn createPlayBinaryStartScripts
tasks.getByName('datahub-frontendDistZip').dependsOn createMainStartScripts
playBinaryDistTar.dependsOn createMainStartScripts
playBinaryDistZip.dependsOn createMainStartScripts
createMainStartScripts.dependsOn 'stageDatahub-frontendDist'
createPlayBinaryTarDist.dependsOn 'stageDatahub-frontendDist'
createPlayBinaryZipDist.dependsOn 'stageDatahub-frontendDist'
createPlayBinaryTarDist.dependsOn 'stageMainDist'
createPlayBinaryZipDist.dependsOn 'stageMainDist'
