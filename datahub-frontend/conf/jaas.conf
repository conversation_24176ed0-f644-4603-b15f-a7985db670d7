// This is a sample JAAS config that uses the following login module
// security.PropertyFileLoginModule -- this module can work with a username and any password defined in the `../conf/user.props` file

WHZ-Authentication {
  security.PropertyFileLoginModule sufficient
    debug="true"
    file="/etc/datahub/plugins/frontend/auth/user.props";
  security.PropertyFileLoginModule sufficient
    debug="true"
    file="/datahub-frontend/conf/user.props";
};