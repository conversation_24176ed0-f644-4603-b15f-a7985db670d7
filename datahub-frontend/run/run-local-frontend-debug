#!/bin/bash

CURRENT_DIR=$(pwd)
BUILD_DIR=../build/stage/main
CONF_DIR=$BUILD_DIR/conf

set -a
source frontend.env
set +a

export JAVA_OPTS="
	-Xms512m
	-Xmx1024m
	-Dhttp.port=$PORT
	-Dconfig.file=$CONF_DIR/application.conf
	-Djava.security.auth.login.config=$CURRENT_DIR/jaas.conf
	-Dlogback.configurationFile=$CURRENT_DIR/logback.xml
	-Dlogback.debug=true
	-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5005"

$BUILD_DIR/bin/datahub-frontend
