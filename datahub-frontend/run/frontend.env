# Server Port
PORT=9002

# Secret Key
DATAHUB_SECRET="YouKnowNothing"

# App version
DATAHUB_APP_VERSION="1.0"

# Play buffer size
DATAHUB_PLAY_MEM_BUFFER_SIZE="10MB"

# Piwik tracking configuration
DATAHUB_PIWIK_SITEID="94" # change_to_your_piwik_id
DATAHUB_PIWIK_URL="//piwik.corp.linkedin.com/piwik/"

# GMS configuration
DATAHUB_GMS_HOST=localhost
DATAHUB_GMS_PORT=8080

# Uncomment to disable analytics tracking
# DATAHUB_ANALYTICS_ENABLED=false

# Kafka Producer Configuration
KAFKA_BOOTSTRAP_SERVER=localhost:9092
DATAHUB_TRACKING_TOPIC=DataHubUsageEvent_v1

# Required Elastic Client Configuration (Analytics)
ELASTIC_CLIENT_HOST=localhost
ELASTIC_CLIENT_PORT=9200

# OIDC Configs
# AUTH_OIDC_ENABLED=true
# AUTH_OIDC_CLIENT_ID=<client-id>>
# AUTH_OIDC_CLIENT_SECRET=<client-secret>
# AUTH_OIDC_DISCOVERY_URI=<idp-domain>/.well-known/openid-configuration
# AUTH_OIDC_BASE_URL=http://localhost:9002
# User and groups provisioning
# AUTH_OIDC_JIT_PROVISIONING_ENABLED=true
# AUTH_OIDC_PRE_PROVISIONING_REQUIRED=false
# AUTH_OIDC_EXTRACT_GROUPS_ENABLED=true
# AUTH_OIDC_GROUPS_CLAIM=groups

# AUTH_JAAS_ENABLED=false

# Change to disable Metadata Service Authentication
# METADATA_SERVICE_AUTH_ENABLED=false

# Change to override max header count defaults
DATAHUB_AKKA_MAX_HEADER_COUNT=64

# Change to override max header value length defaults
DATAHUB_AKKA_MAX_HEADER_VALUE_LENGTH=8k