package com.linkedin.datahub.graphql.resolvers.type;

import graphql.TypeResolutionEnvironment;
import graphql.schema.GraphQLObjectType;
import graphql.schema.TypeResolver;

public class TimeSeriesAspectInterfaceTypeResolver implements TypeResolver {

  public TimeSeriesAspectInterfaceTypeResolver() {}

  @Override
  public GraphQLObjectType getType(TypeResolutionEnvironment env) {
    // TODO(John): Fill this out.
    return null;
  }
}
