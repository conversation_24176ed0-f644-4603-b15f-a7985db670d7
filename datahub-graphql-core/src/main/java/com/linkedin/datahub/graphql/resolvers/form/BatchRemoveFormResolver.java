package com.linkedin.datahub.graphql.resolvers.form;

import static com.linkedin.datahub.graphql.resolvers.ResolverUtils.bindArgument;

import com.datahub.authentication.Authentication;
import com.linkedin.common.urn.Urn;
import com.linkedin.common.urn.UrnUtils;
import com.linkedin.datahub.graphql.QueryContext;
import com.linkedin.datahub.graphql.concurrency.GraphQLConcurrencyUtils;
import com.linkedin.datahub.graphql.generated.BatchAssignFormInput;
import com.linkedin.metadata.service.FormService;
import graphql.schema.DataFetcher;
import graphql.schema.DataFetchingEnvironment;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;

public class BatchRemoveFormResolver implements DataFetcher<CompletableFuture<Boolean>> {

  private final FormService _formService;

  public BatchRemoveFormResolver(@Nonnull final FormService formService) {
    _formService = Objects.requireNonNull(formService, "formService must not be null");
  }

  @Override
  public CompletableFuture<Boolean> get(final DataFetchingEnvironment environment)
      throws Exception {
    final QueryContext context = environment.getContext();

    final BatchAssignFormInput input =
        bindArgument(environment.getArgument("input"), BatchAssignFormInput.class);
    final Urn formUrn = UrnUtils.getUrn(input.getFormUrn());
    final List<String> entityUrns = input.getEntityUrns();
    final Authentication authentication = context.getAuthentication();

    // TODO: (PRD-1062) Add permission check once permission exists

    return GraphQLConcurrencyUtils.supplyAsync(
        () -> {
          try {
            _formService.batchUnassignFormForEntities(
                context.getOperationContext(),
                entityUrns.stream().map(UrnUtils::getUrn).collect(Collectors.toList()),
                formUrn);
            return true;
          } catch (Exception e) {
            throw new RuntimeException(
                String.format("Failed to perform update against input %s", input), e);
          }
        },
        this.getClass().getSimpleName(),
        "get");
  }
}
